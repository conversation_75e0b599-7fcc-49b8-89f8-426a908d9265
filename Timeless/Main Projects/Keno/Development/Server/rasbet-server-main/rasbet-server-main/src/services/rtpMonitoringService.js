import ShopRTP from "../models/ShopRTP.js";
import Shop from "../models/Shop.js";
import logger from "../lib/logger.js";

/**
 * Enhanced RTP Monitoring and Adjustment Service
 * Provides real-time RTP tracking and automatic parameter adjustment
 */

// RTP monitoring thresholds
const RTP_THRESHOLDS = {
  CRITICAL_LOW: 0.1,    // 10% below target
  WARNING_LOW: 0.05,    // 5% below target
  WARNING_HIGH: 0.05,   // 5% above target
  CRITICAL_HIGH: 0.1,   // 10% above target
};

// Adjustment parameters
const ADJUSTMENT_FACTORS = {
  SENSITIVITY_INCREASE: 1.2,
  SENSITIVITY_DECREASE: 0.8,
  MAX_SENSITIVITY: 5.0,
  MIN_SENSITIVITY: 0.5,
  PROBABILITY_ADJUSTMENT: 0.1,
};

/**
 * Calculate current RTP for a shop
 */
export const calculateShopRTP = async (shopId) => {
  const shopRtpData = await ShopRTP.findOne({ shop: shopId }).lean();
  
  if (!shopRtpData || shopRtpData.totalBets <= 0) {
    return {
      currentRtp: 1.0, // Default to 100% when no data
      totalBets: 0,
      totalPayouts: 0,
    };
  }
  
  return {
    currentRtp: shopRtpData.totalPayouts / shopRtpData.totalBets,
    totalBets: shopRtpData.totalBets,
    totalPayouts: shopRtpData.totalPayouts,
  };
};

/**
 * Get target RTP for a shop
 */
export const getTargetRTP = async (shopId) => {
  const shop = await Shop.findById(shopId).lean();
  if (!shop) {
    throw new Error(`Shop not found: ${shopId}`);
  }
  
  const profitMargin = shop.configuration?.profitMargin || 15;
  const minSafeRtp = shop.configuration?.minSafeRTP || 80;
  
  const profitTargetRtp = (100 - profitMargin) / 100;
  const minimumFloorRtp = minSafeRtp / 100;
  
  return Math.max(profitTargetRtp, minimumFloorRtp);
};

/**
 * Analyze RTP status and determine if adjustments are needed
 */
export const analyzeRTPStatus = async (shopId) => {
  const { currentRtp, totalBets, totalPayouts } = await calculateShopRTP(shopId);
  const targetRtp = await getTargetRTP(shopId);
  
  const deviation = targetRtp - currentRtp;
  const deviationPercent = Math.abs(deviation);
  
  let status = 'NORMAL';
  let severity = 0;
  let recommendedAction = 'NONE';
  
  if (deviation > RTP_THRESHOLDS.CRITICAL_LOW) {
    status = 'CRITICAL_LOW';
    severity = 3;
    recommendedAction = 'INCREASE_PAYOUTS';
  } else if (deviation > RTP_THRESHOLDS.WARNING_LOW) {
    status = 'WARNING_LOW';
    severity = 2;
    recommendedAction = 'SLIGHT_INCREASE_PAYOUTS';
  } else if (deviation < -RTP_THRESHOLDS.WARNING_HIGH) {
    status = 'WARNING_HIGH';
    severity = 2;
    recommendedAction = 'SLIGHT_DECREASE_PAYOUTS';
  } else if (deviation < -RTP_THRESHOLDS.CRITICAL_HIGH) {
    status = 'CRITICAL_HIGH';
    severity = 3;
    recommendedAction = 'DECREASE_PAYOUTS';
  }
  
  return {
    shopId,
    currentRtp,
    targetRtp,
    deviation,
    deviationPercent,
    status,
    severity,
    recommendedAction,
    totalBets,
    totalPayouts,
    dataQuality: totalBets > 1000 ? 'HIGH' : totalBets > 100 ? 'MEDIUM' : 'LOW',
  };
};

/**
 * Generate dynamic configuration adjustments based on RTP analysis
 */
export const generateConfigAdjustments = async (shopId) => {
  const analysis = await analyzeRTPStatus(shopId);
  const adjustments = {};
  
  // Only make adjustments if we have sufficient data
  if (analysis.dataQuality === 'LOW') {
    return { adjustments: {}, reason: 'Insufficient data for adjustments' };
  }
  
  switch (analysis.recommendedAction) {
    case 'INCREASE_PAYOUTS':
      adjustments.SENSITIVITY_FACTOR = Math.min(
        ADJUSTMENT_FACTORS.MAX_SENSITIVITY,
        2.0 * ADJUSTMENT_FACTORS.SENSITIVITY_INCREASE
      );
      adjustments.HIGHER_PAYOUT_FACTOR = Math.min(0.95, 0.9 + ADJUSTMENT_FACTORS.PROBABILITY_ADJUSTMENT);
      adjustments.LOWER_PAYOUT_FACTOR = Math.max(0.05, 0.1 - ADJUSTMENT_FACTORS.PROBABILITY_ADJUSTMENT);
      break;
      
    case 'SLIGHT_INCREASE_PAYOUTS':
      adjustments.SENSITIVITY_FACTOR = Math.min(
        ADJUSTMENT_FACTORS.MAX_SENSITIVITY,
        2.0 * 1.1
      );
      adjustments.HIGHER_PAYOUT_FACTOR = Math.min(0.95, 0.9 + ADJUSTMENT_FACTORS.PROBABILITY_ADJUSTMENT * 0.5);
      break;
      
    case 'SLIGHT_DECREASE_PAYOUTS':
      adjustments.SENSITIVITY_FACTOR = Math.max(
        ADJUSTMENT_FACTORS.MIN_SENSITIVITY,
        2.0 * 0.9
      );
      adjustments.LOWER_PAYOUT_FACTOR = Math.min(0.3, 0.1 + ADJUSTMENT_FACTORS.PROBABILITY_ADJUSTMENT * 0.5);
      break;
      
    case 'DECREASE_PAYOUTS':
      adjustments.SENSITIVITY_FACTOR = Math.max(
        ADJUSTMENT_FACTORS.MIN_SENSITIVITY,
        2.0 * ADJUSTMENT_FACTORS.SENSITIVITY_DECREASE
      );
      adjustments.HIGHER_PAYOUT_FACTOR = Math.max(0.6, 0.9 - ADJUSTMENT_FACTORS.PROBABILITY_ADJUSTMENT);
      adjustments.LOWER_PAYOUT_FACTOR = Math.min(0.3, 0.1 + ADJUSTMENT_FACTORS.PROBABILITY_ADJUSTMENT);
      break;
      
    default:
      // No adjustments needed
      break;
  }
  
  return {
    analysis,
    adjustments,
    reason: `RTP ${analysis.status}: Current ${(analysis.currentRtp * 100).toFixed(2)}%, Target ${(analysis.targetRtp * 100).toFixed(2)}%`,
  };
};

/**
 * Monitor all shops and generate alerts
 */
export const monitorAllShopsRTP = async () => {
  try {
    const shops = await Shop.find({ isActive: true }).lean();
    const alerts = [];
    
    for (const shop of shops) {
      try {
        const analysis = await analyzeRTPStatus(shop._id);
        
        if (analysis.severity >= 2) {
          alerts.push({
            shopId: shop._id,
            shopName: shop.name,
            ...analysis,
            timestamp: new Date(),
          });
          
          // Log warnings and critical issues
          const logLevel = analysis.severity >= 3 ? 'error' : 'warn';
          logger[logLevel](
            `RTP ${analysis.status} for shop ${shop.name} (${shop._id}): ` +
            `Current=${(analysis.currentRtp * 100).toFixed(2)}%, ` +
            `Target=${(analysis.targetRtp * 100).toFixed(2)}%, ` +
            `Deviation=${(analysis.deviationPercent * 100).toFixed(2)}%, ` +
            `Action=${analysis.recommendedAction}`
          );
        }
      } catch (error) {
        logger.error(`Failed to analyze RTP for shop ${shop._id}:`, error);
      }
    }
    
    return alerts;
  } catch (error) {
    logger.error('Failed to monitor shops RTP:', error);
    throw error;
  }
};

/**
 * Get comprehensive RTP report for a shop
 */
export const getShopRTPReport = async (shopId) => {
  const analysis = await analyzeRTPStatus(shopId);
  const { adjustments, reason } = await generateConfigAdjustments(shopId);
  
  return {
    ...analysis,
    recommendedAdjustments: adjustments,
    adjustmentReason: reason,
    reportTimestamp: new Date(),
  };
};

/**
 * Validate RTP prediction accuracy
 */
export const validateRTPPrediction = async (shopId, predictedRtp, actualPayout, roundBets) => {
  const { currentRtp, totalBets, totalPayouts } = await calculateShopRTP(shopId);
  
  // Calculate what the actual RTP would be after this round
  const newTotalBets = totalBets + roundBets;
  const newTotalPayouts = totalPayouts + actualPayout;
  const actualNewRtp = newTotalBets > 0 ? newTotalPayouts / newTotalBets : 1.0;
  
  const predictionError = Math.abs(predictedRtp - actualNewRtp);
  const errorPercent = (predictionError * 100);
  
  // Log prediction accuracy
  if (errorPercent > 2.0) { // More than 2% error
    logger.warn(
      `RTP prediction error for shop ${shopId}: ` +
      `Predicted=${(predictedRtp * 100).toFixed(2)}%, ` +
      `Actual=${(actualNewRtp * 100).toFixed(2)}%, ` +
      `Error=${errorPercent.toFixed(2)}%`
    );
  }
  
  return {
    predictedRtp,
    actualRtp: actualNewRtp,
    predictionError,
    errorPercent,
    accuracy: Math.max(0, 100 - errorPercent),
  };
};
