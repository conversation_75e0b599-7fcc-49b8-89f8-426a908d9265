import { runSimulation, analyzeRtpImpact, testConvergenceBehavior } from "game_engine";
import logger from "../lib/logger.js";

/**
 * RTP Testing and Validation Service
 * Provides comprehensive testing for the enhanced RTP algorithm
 */

/**
 * Generate test scenarios for RTP validation
 */
const generateTestScenarios = () => {
  return [
    {
      name: "Low RTP Recovery",
      shopFinancials: {
        profitMargin: 15,
        minSafeRtp: 80,
        currentWindowBets: 10000,
        currentWindowPayouts: 7000, // 70% RTP - needs recovery
        currentRoundBets: 500,
      },
      expectedBehavior: "Should favor higher payouts to recover RTP",
    },
    {
      name: "High RTP Control",
      shopFinancials: {
        profitMargin: 15,
        minSafeRtp: 80,
        currentWindowBets: 10000,
        currentWindowPayouts: 9000, // 90% RTP - needs control
        currentRoundBets: 500,
      },
      expectedBehavior: "Should favor lower payouts to control RTP",
    },
    {
      name: "Target RTP Maintenance",
      shopFinancials: {
        profitMargin: 15,
        minSafeRtp: 80,
        currentWindowBets: 10000,
        currentWindowPayouts: 8500, // 85% RTP - at target
        currentRoundBets: 500,
      },
      expectedBehavior: "Should maintain current RTP level",
    },
    {
      name: "Critical Low RTP",
      shopFinancials: {
        profitMargin: 15,
        minSafeRtp: 80,
        currentWindowBets: 10000,
        currentWindowPayouts: 6500, // 65% RTP - critical
        currentRoundBets: 500,
      },
      expectedBehavior: "Should strongly favor higher payouts with safety net",
    },
    {
      name: "New Shop (No History)",
      shopFinancials: {
        profitMargin: 15,
        minSafeRtp: 80,
        currentWindowBets: 0,
        currentWindowPayouts: 0,
        currentRoundBets: 500,
      },
      expectedBehavior: "Should use default behavior for new shops",
    },
  ];
};

/**
 * Generate test tickets with various bet patterns
 */
const generateTestTickets = (scenario) => {
  const tickets = [];
  
  // Add number selection tickets
  for (let i = 1; i <= 5; i++) {
    tickets.push({
      betType: null,
      selectedNumbers: Array.from({ length: i }, (_, idx) => idx + 1),
      stake: 100,
    });
  }
  
  // Add heads/tails/evens tickets
  tickets.push(
    { betType: "HEADS", selectedNumbers: null, stake: 50 },
    { betType: "TAILS", selectedNumbers: null, stake: 50 },
    { betType: "EVENS", selectedNumbers: null, stake: 25 }
  );
  
  return tickets;
};

/**
 * Test RTP convergence over multiple rounds
 */
export const testRTPConvergence = async (scenario, rounds = 100) => {
  const config = {
    maxDrawAttempts: 1000,
    sensitivityFactor: 2.0,
    minPayoutProbability: 0.1,
    maxPayoutProbability: 0.9,
  };
  
  const tickets = generateTestTickets(scenario);
  let shopFinancials = { ...scenario.shopFinancials };
  const results = [];
  
  logger.info(`Testing RTP convergence for scenario: ${scenario.name}`);
  
  for (let round = 0; round < rounds; round++) {
    const simulationInput = {
      config,
      shopFinancials,
      tickets,
    };
    
    try {
      const result = runSimulation(simulationInput);
      
      // Update shop financials for next round
      shopFinancials.currentWindowBets += shopFinancials.currentRoundBets;
      shopFinancials.currentWindowPayouts += result.finalPayout;
      
      const currentRtp = shopFinancials.currentWindowPayouts / shopFinancials.currentWindowBets;
      const targetRtp = Math.max(
        (100 - shopFinancials.profitMargin) / 100,
        shopFinancials.minSafeRtp / 100
      );
      
      results.push({
        round,
        currentRtp,
        targetRtp,
        deviation: Math.abs(targetRtp - currentRtp),
        payout: result.finalPayout,
        predictedRtp: result.predictedRtp,
        iterations: result.iterationsUsed,
        convergenceScore: result.convergenceScore,
      });
      
    } catch (error) {
      logger.error(`Error in round ${round}:`, error);
      break;
    }
  }
  
  return analyzeConvergenceResults(results, scenario);
};

/**
 * Analyze convergence test results
 */
const analyzeConvergenceResults = (results, scenario) => {
  if (results.length === 0) {
    return { error: "No results to analyze" };
  }
  
  const finalResult = results[results.length - 1];
  const initialDeviation = results[0]?.deviation || 0;
  const finalDeviation = finalResult.deviation;
  
  // Calculate convergence metrics
  const convergenceRate = initialDeviation > 0 ? 
    (initialDeviation - finalDeviation) / initialDeviation : 0;
  
  const avgIterations = results.reduce((sum, r) => sum + r.iterations, 0) / results.length;
  const avgConvergenceScore = results.reduce((sum, r) => sum + r.convergenceScore, 0) / results.length;
  
  // Check if RTP stayed within acceptable bounds
  const deviationHistory = results.map(r => r.deviation);
  const maxDeviation = Math.max(...deviationHistory);
  const stableRounds = results.filter(r => r.deviation < 0.05).length;
  const stabilityRate = stableRounds / results.length;
  
  return {
    scenario: scenario.name,
    totalRounds: results.length,
    initialDeviation,
    finalDeviation,
    convergenceRate,
    maxDeviation,
    stabilityRate,
    avgIterations,
    avgConvergenceScore,
    finalRtp: finalResult.currentRtp,
    targetRtp: finalResult.targetRtp,
    success: finalDeviation < 0.02 && stabilityRate > 0.8, // Success criteria
    results: results.slice(-10), // Last 10 rounds for detailed analysis
  };
};

/**
 * Test algorithm performance under stress
 */
export const testAlgorithmStress = async () => {
  const stressTests = [
    {
      name: "High Volume Betting",
      modifications: {
        currentRoundBets: 10000,
        ticketCount: 100,
      },
    },
    {
      name: "Extreme RTP Deviation",
      modifications: {
        currentWindowPayouts: 3000, // 30% RTP
        currentWindowBets: 10000,
      },
    },
    {
      name: "High Iteration Count",
      modifications: {
        maxDrawAttempts: 10000,
      },
    },
  ];
  
  const results = [];
  
  for (const test of stressTests) {
    logger.info(`Running stress test: ${test.name}`);
    
    const baseScenario = generateTestScenarios()[0];
    const modifiedScenario = {
      ...baseScenario,
      shopFinancials: {
        ...baseScenario.shopFinancials,
        ...test.modifications,
      },
    };
    
    const startTime = Date.now();
    
    try {
      const result = await testRTPConvergence(modifiedScenario, 50);
      const endTime = Date.now();
      
      results.push({
        testName: test.name,
        duration: endTime - startTime,
        success: result.success,
        finalDeviation: result.finalDeviation,
        stabilityRate: result.stabilityRate,
        avgIterations: result.avgIterations,
      });
      
    } catch (error) {
      logger.error(`Stress test ${test.name} failed:`, error);
      results.push({
        testName: test.name,
        error: error.message,
        success: false,
      });
    }
  }
  
  return results;
};

/**
 * Run comprehensive RTP validation suite
 */
export const runRTPValidationSuite = async () => {
  logger.info("Starting comprehensive RTP validation suite...");
  
  const scenarios = generateTestScenarios();
  const validationResults = {
    timestamp: new Date(),
    scenarios: [],
    stressTests: [],
    summary: {},
  };
  
  // Test each scenario
  for (const scenario of scenarios) {
    try {
      const result = await testRTPConvergence(scenario);
      validationResults.scenarios.push(result);
      
      logger.info(
        `Scenario "${scenario.name}": ` +
        `${result.success ? 'PASSED' : 'FAILED'} ` +
        `(Final Deviation: ${(result.finalDeviation * 100).toFixed(2)}%, ` +
        `Stability: ${(result.stabilityRate * 100).toFixed(1)}%)`
      );
      
    } catch (error) {
      logger.error(`Scenario ${scenario.name} failed:`, error);
      validationResults.scenarios.push({
        scenario: scenario.name,
        error: error.message,
        success: false,
      });
    }
  }
  
  // Run stress tests
  try {
    validationResults.stressTests = await testAlgorithmStress();
  } catch (error) {
    logger.error("Stress testing failed:", error);
  }
  
  // Generate summary
  const successfulScenarios = validationResults.scenarios.filter(s => s.success).length;
  const successfulStressTests = validationResults.stressTests.filter(s => s.success).length;
  
  validationResults.summary = {
    totalScenarios: scenarios.length,
    successfulScenarios,
    scenarioSuccessRate: successfulScenarios / scenarios.length,
    totalStressTests: validationResults.stressTests.length,
    successfulStressTests,
    stressTestSuccessRate: successfulStressTests / validationResults.stressTests.length,
    overallSuccess: successfulScenarios === scenarios.length && 
                   successfulStressTests === validationResults.stressTests.length,
  };
  
  logger.info(
    `RTP Validation Suite Complete: ` +
    `Scenarios ${successfulScenarios}/${scenarios.length}, ` +
    `Stress Tests ${successfulStressTests}/${validationResults.stressTests.length}`
  );
  
  return validationResults;
};

/**
 * Quick RTP algorithm health check
 */
export const quickRTPHealthCheck = async () => {
  const scenario = generateTestScenarios()[0]; // Use first scenario
  const result = await testRTPConvergence(scenario, 10); // Quick 10-round test
  
  return {
    healthy: result.success,
    finalDeviation: result.finalDeviation,
    stabilityRate: result.stabilityRate,
    avgIterations: result.avgIterations,
    timestamp: new Date(),
  };
};
