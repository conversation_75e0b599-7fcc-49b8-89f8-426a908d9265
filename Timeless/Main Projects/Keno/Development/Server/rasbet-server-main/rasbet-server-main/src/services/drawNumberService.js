import Bet from "../models/Bet.js";
import Admin from "../models/Admin.js";
import ShopRTP from "../models/ShopRTP.js";
import Shop from "../models/Shop.js";
import Game from "../models/Game.js";
import { convertIdIntoValidId } from "./gameService.js";
import logger from "../lib/logger.js";
import { runSimulation } from "game_engine";

// --- CONFIGURATION & CONSTANTS ---
const GAME_DEFAULTS = {
  SENSITIVITY_FACTOR: 2,
  MAX_DRAW_ATTEMPTS: 5000,
  MIN_PAYOUT_PROBABILITY: 0.1,
  MAX_PAYOUT_PROBABILITY: 0.9,
};

const SHOP_DEFAULTS = {
  PROFIT_MARGIN: 15,
  MIN_SAFE_RTP: 80,
};

/**
 * Fetches and aggregates financial data for a single shop.
 */
const fetchFinancialData = async (shopId, eventNumber) => {
  const validShopId = convertIdIntoValidId(shopId);

  const shop = await Shop.findById(validShopId).lean();
  if (!shop) {
    throw new Error(`Shop or configuration not found for shopId: ${shopId}`);
  }

  const currentGame = await Game.findOne({
    shop: validShopId,
    eventNumber,
  }).lean();
  if (!currentGame) {
    throw new Error(`Game not found for shop ${shopId}, event ${eventNumber}.`);
  }

  const shopRtpData = await ShopRTP.findOne({ shop: validShopId }).lean();
  const currentWindowBets = shopRtpData?.totalBets || 0;
  const currentWindowPayouts = shopRtpData?.totalPayouts || 0;

  const betSlips = await Bet.find({
    game: currentGame._id,
    "tickets.status": "ACTIVE",
  }).lean();

  const shopData = {
    profitMargin:
      shop.configuration?.profitMargin ?? SHOP_DEFAULTS.PROFIT_MARGIN,
    minSafeRtp: shop.configuration?.minSafeRTP ?? SHOP_DEFAULTS.MIN_SAFE_RTP,
    currentWindowBets,
    currentWindowPayouts,
    currentRoundBets: betSlips
      .flatMap((bet) => bet.tickets)
      .filter((ticket) => ticket.status === "ACTIVE")
      .reduce((total, ticket) => total + ticket.stake, 0),
  };

  return {
    shopData,
    tickets: betSlips
      .flatMap((bet) => bet.tickets)
      .filter((ticket) => ticket.status === "ACTIVE"),
  };
};

/**
 * Main draw function for a single shop.
 */
export const drawNumberService = async (shopId, eventNumber) => {
  logger.info(
    `--- Starting draw process for Shop ${shopId}, Event #${eventNumber} ---`,
  );

  // STEP 1: FETCH DATA FROM DATABASE
  const admin = await Admin.findOne().lean();
  if (!admin?.config) {
    throw new Error("Game configuration not found.");
  }

  const shopSystemData = await fetchFinancialData(shopId, eventNumber);

  // Handle edge case: No active bets.
  if (shopSystemData.shopData.currentRoundBets === 0) {
    const { finalDraw } = runSimulation({
      config: {
        maxDrawAttempts: 1,
        sensitivityFactor: 0,
        minPayoutProbability: 0,
        maxPayoutProbability: 0,
      },
      shopFinancials: shopSystemData.shopData,
      tickets: [],
    });

    logger.info(
      `No active bets for shop ${shopId}, Event #${eventNumber}. Using a random draw. [${finalDraw.join(
        ",",
      )}]`,
    );
    return finalDraw;
  }

  // STEP 2: PREPARE THE INPUT OBJECT FOR THE RUST ENGINE
  const simulationInput = {
    config: {
      maxDrawAttempts:
        admin.config.MAX_DRAW_ATTEMPTS ?? GAME_DEFAULTS.MAX_DRAW_ATTEMPTS,
      sensitivityFactor:
        admin.config.SENSITIVITY_FACTOR ?? GAME_DEFAULTS.SENSITIVITY_FACTOR,
      minPayoutProbability:
        admin.config.LOWER_PAYOUT_FACTOR ??
        GAME_DEFAULTS.MIN_PAYOUT_PROBABILITY,
      maxPayoutProbability:
        admin.config.HIGHER_PAYOUT_FACTOR ??
        GAME_DEFAULTS.MAX_PAYOUT_PROBABILITY,
    },
    shopFinancials: shopSystemData.shopData,
    tickets: shopSystemData.tickets.map((t) => ({
      betType: t.betType,
      selectedNumbers: t.selectedNumbers,
      stake: t.stake,
    })),
  };

  // STEP 3: CALL THE RUST ENGINE
  const {
    finalDraw,
    finalPayout,
    predictedRtp,
    rtpDeviation,
    iterationsUsed,
    convergenceScore,
  } = runSimulation(simulationInput);

  // Enhanced logging with RTP metrics
  logger.info(
    `Draw completed for ${shopId}, Event #${eventNumber}: ` +
    `Numbers=[${finalDraw.join(",")}], ` +
    `Payout=${finalPayout.toFixed(2)}, ` +
    `PredictedRTP=${(predictedRtp * 100).toFixed(2)}%, ` +
    `Deviation=${(rtpDeviation * 100).toFixed(2)}%, ` +
    `Iterations=${iterationsUsed}, ` +
    `Convergence=${convergenceScore.toFixed(4)}`,
  );

  // RTP Warning System
  const targetRtp = Math.max(
    (100 - shopSystemData.shopData.profitMargin) / 100,
    shopSystemData.shopData.minSafeRtp / 100
  );

  if (rtpDeviation > 0.05) {
    logger.warn(
      `HIGH RTP DEVIATION for shop ${shopId}: ` +
      `Target=${(targetRtp * 100).toFixed(2)}%, ` +
      `Predicted=${(predictedRtp * 100).toFixed(2)}%, ` +
      `Deviation=${(rtpDeviation * 100).toFixed(2)}%`
    );
  }

  if (predictedRtp < targetRtp - 0.1) {
    logger.error(
      `CRITICAL RTP BELOW SAFE THRESHOLD for shop ${shopId}: ` +
      `Predicted=${(predictedRtp * 100).toFixed(2)}%, ` +
      `Target=${(targetRtp * 100).toFixed(2)}%`
    );
  }

  // STEP 4: UPDATE FINANCIALS
  const { currentRoundBets } = shopSystemData.shopData;
  if (currentRoundBets > 0 || finalPayout > 0) {
    try {
      await ShopRTP.findOneAndUpdate(
        { shop: convertIdIntoValidId(shopId) },
        { $inc: { totalBets: currentRoundBets, totalPayouts: finalPayout } },
        { upsert: true },
      );
      logger.info(
        `ShopRTP updated for shop ${shopId}. Bets: ${currentRoundBets}, Payout: ${finalPayout}`,
      );
    } catch (error) {
      logger.error(`ShopRTP update failed for shop ${shopId}`, error);
      throw error;
    }
  }

  return finalDraw;
};
