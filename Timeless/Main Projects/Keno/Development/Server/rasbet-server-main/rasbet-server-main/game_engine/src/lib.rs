use napi::Result;
use napi_derive::napi;
use rand::seq::SliceRandom;
use rand::Rng;
use serde::Deserialize;
use std::collections::{HashSet};
use std::cmp::Ordering;

//======================================================================
//==                       DATA STRUCTURES                            ==
//======================================================================

#[napi(object)]
#[derive(Deserialize, Clone)]
#[serde(rename_all = "camelCase")]
pub struct Config {
    pub max_draw_attempts: u32,
    pub sensitivity_factor: f64,
    pub min_payout_probability: f64,
    pub max_payout_probability: f64,
}

#[napi(object)]
#[derive(Deserialize, Clone)]
#[serde(rename_all = "camelCase")]
pub struct Ticket {
    pub bet_type: Option<String>,
    pub selected_numbers: Option<Vec<u8>>,
    pub stake: f64,
}

#[napi(object)]
#[derive(Deserialize, Clone)]
#[serde(rename_all = "camelCase")]
pub struct ShopFinancials {
    pub profit_margin: f64,
    pub min_safe_rtp: f64,
    pub current_window_bets: f64,
    pub current_window_payouts: f64,
    pub current_round_bets: f64,
}

#[napi(object)]
#[derive(Deserialize, Clone)]
#[serde(rename_all = "camelCase")]
pub struct SimulationInput {
    pub config: Config,
    pub shop_financials: ShopFinancials,
    pub tickets: Vec<Ticket>,
}

#[napi(object)]
pub struct SimulationOutput {
    pub final_draw: Vec<u8>,
    pub final_payout: f64,
}


//======================================================================
//==                       HELPER FUNCTIONS                           ==
//======================================================================

static PRIZE_STRUCTURE: phf::Map<u8, phf::Map<u8, f64>> = phf::phf_map! {
    1u8 => phf::phf_map! { 1u8 => 4.0, 0u8 => 0.0 },
    2u8 => phf::phf_map! { 2u8 => 25.0, 1u8 => 1.0, 0u8 => 0.0 },
    3u8 => phf::phf_map! { 3u8 => 50.0, 2u8 => 5.0, 1u8 => 0.0, 0u8 => 0.0 },
    4u8 => phf::phf_map! { 4u8 => 110.0, 3u8 => 20.0, 2u8 => 1.0, 1u8 => 0.0, 0u8 => 0.0 },
    5u8 => phf::phf_map! { 5u8 => 900.0, 4u8 => 15.0, 3u8 => 3.0, 2u8 => 1.0, 1u8 => 0.0, 0u8 => 0.0 },
};

const TOTAL_NUMBERS: u8 = 80;
const DRAW_COUNT: usize = 20;
const HEADS_TAILS_MULTIPLIER: f64 = 2.0;
const EVENS_MULTIPLIER: f64 = 4.0;
const RTP_SAFETY_NET_BONUS: f64 = 2.0; // Constant from multi-shop logic

#[derive(PartialEq)]
enum HeadsTailsEvensOutcome { Heads, Tails, Evens }

fn get_heads_tails_evens(drawn_numbers: &[u8]) -> HeadsTailsEvensOutcome {
    let count_under_41 = drawn_numbers.iter().filter(|&&n| n < 41).count();
    if count_under_41 > 10 { HeadsTailsEvensOutcome::Heads }
    else if count_under_41 < 10 { HeadsTailsEvensOutcome::Tails }
    else { HeadsTailsEvensOutcome::Evens }
}

fn calculate_winnings(selected_count: u8, matched_count: u8, bet_amount: f64) -> f64 {
    PRIZE_STRUCTURE
        .get(&selected_count)
        .and_then(|matches_map| matches_map.get(&matched_count))
        .map_or(0.0, |multiplier| multiplier * bet_amount)
}

fn generate_random_draw() -> Vec<u8> {
    let mut nums: Vec<u8> = (1..=TOTAL_NUMBERS).collect();
    let mut rng = rand::rng();
    nums.shuffle(&mut rng);
    nums.into_iter().take(DRAW_COUNT).collect()
}

fn simulate_payout_for_tickets(drawn_numbers: &[u8], tickets: &[Ticket]) -> f64 {
    let mut total_payout = 0.0;
    let drawn_set: HashSet<_> = drawn_numbers.iter().cloned().collect();
    let outcome = get_heads_tails_evens(drawn_numbers);

    for ticket in tickets {
        if let Some(bet_type) = &ticket.bet_type {
            let multiplier = match bet_type.as_str() {
                "HEADS" if outcome == HeadsTailsEvensOutcome::Heads => HEADS_TAILS_MULTIPLIER,
                "TAILS" if outcome == HeadsTailsEvensOutcome::Tails => HEADS_TAILS_MULTIPLIER,
                "EVENS" if outcome == HeadsTailsEvensOutcome::Evens => EVENS_MULTIPLIER,
                _ => 0.0,
            };
            total_payout += ticket.stake * multiplier;
        } else if let Some(selected_numbers) = &ticket.selected_numbers {
            let matches = selected_numbers.iter().filter(|n| drawn_set.contains(n)).count();
            total_payout += calculate_winnings(selected_numbers.len() as u8, matches as u8, ticket.stake);
        }
    }
    total_payout
}

fn calculate_expected_probability(shop: &ShopFinancials, config: &Config) -> f64 {
    if shop.current_window_bets <= 0.0 {
        return 0.5;
    }

    let profit_target_rtp = (100.0 - shop.profit_margin) / 100.0;
    let minimum_floor_rtp = shop.min_safe_rtp / 100.0;
    let target_rtp = profit_target_rtp.max(minimum_floor_rtp);

    let current_rtp = shop.current_window_payouts / shop.current_window_bets;
    let rtp_deviation = target_rtp - current_rtp;
    let probability = 0.5 + rtp_deviation * config.sensitivity_factor;

    probability.clamp(config.min_payout_probability, config.max_payout_probability)
}


//======================================================================
//==                       MAIN N-API FUNCTION                        ==
//======================================================================

#[napi]
pub fn run_simulation(input: SimulationInput) -> Result<SimulationOutput> {
    // Handle edge case of no bets in the current round
    if input.shop_financials.current_round_bets <= 0.0 {
        return Ok(SimulationOutput {
            final_draw: generate_random_draw(),
            final_payout: 0.0,
        });
    }

    // This is the desired probability of a "high payout" draw.
    // We will use it to score candidates.
    let expected_probability = calculate_expected_probability(&input.shop_financials, &input.config);

    let mut best_draw: Option<Vec<u8>> = None;
    let mut best_score = f64::NEG_INFINITY;

    // Iterate through a number of random draws, keeping the one with the best "fitness score".
    // This uses an iterative search to find the "fittest" candidate.
    for _ in 0..input.config.max_draw_attempts {
        let candidate_draw = generate_random_draw();
        let payout = simulate_payout_for_tickets(&candidate_draw, &input.tickets);

        // --- Start of Fitness Score Calculation (from multi-shop logic) ---
        let mut current_score = 0.0;
        let shop = &input.shop_financials;

        let actual_payout_ratio = if shop.current_round_bets > 0.0 {
            payout / shop.current_round_bets
        } else {
            0.0
        };

        // The core fitness logic: reward draws that align with the expected probability.
        // If we want a high payout, a high-payout draw gets a high score.
        // If we want a low payout, a low-payout draw gets a high score.
        if actual_payout_ratio >= 1.0 {
            current_score += expected_probability;
        } else {
            current_score += 1.0 - expected_probability;
        }

        // Add the safety net bonus for shops with very low historical RTP.
        // This encourages a near-win to boost player morale and shop RTP.
        let current_rtp = if shop.current_window_bets > 0.0 {
            shop.current_window_payouts / shop.current_window_bets
        } else {
            1.0 // Default to 100% RTP if there are no historical bets
        };

        if current_rtp < shop.min_safe_rtp / 100.0 && actual_payout_ratio >= 0.9 {
            current_score += RTP_SAFETY_NET_BONUS;
        }
        // --- End of Fitness Score Calculation ---

        // If this draw is the best we've seen so far, save it.
        if current_score > best_score {
            best_score = current_score;
            best_draw = Some(candidate_draw);
        }
    }

    // After the loop, select the best draw found. If none were found (e.g., max_draw_attempts=0),
    // generate a new random one as a fallback.
    let final_draw = best_draw.unwrap_or_else(generate_random_draw);
    let final_payout = simulate_payout_for_tickets(&final_draw, &input.tickets);

    Ok(SimulationOutput {
        final_draw,
        final_payout,
    })
}