use napi::Result;
use napi_derive::napi;
use rand::seq::SliceRandom;
use rand::Rng;
use serde::Deserialize;
use std::collections::{HashSet};
use std::cmp::Ordering;

//======================================================================
//==                       DATA STRUCTURES                            ==
//======================================================================

#[napi(object)]
#[derive(Deserialize, Clone)]
#[serde(rename_all = "camelCase")]
pub struct Config {
    pub max_draw_attempts: u32,
    pub sensitivity_factor: f64,
    pub min_payout_probability: f64,
    pub max_payout_probability: f64,
}

#[napi(object)]
#[derive(Deserialize, Clone)]
#[serde(rename_all = "camelCase")]
pub struct Ticket {
    pub bet_type: Option<String>,
    pub selected_numbers: Option<Vec<u8>>,
    pub stake: f64,
}

#[napi(object)]
#[derive(Deserialize, Clone)]
#[serde(rename_all = "camelCase")]
pub struct ShopFinancials {
    pub profit_margin: f64,
    pub min_safe_rtp: f64,
    pub current_window_bets: f64,
    pub current_window_payouts: f64,
    pub current_round_bets: f64,
}

#[napi(object)]
#[derive(Deserialize, Clone)]
#[serde(rename_all = "camelCase")]
pub struct SimulationInput {
    pub config: Config,
    pub shop_financials: ShopFinancials,
    pub tickets: Vec<Ticket>,
}

#[napi(object)]
pub struct SimulationOutput {
    pub final_draw: Vec<u8>,
    pub final_payout: f64,
    pub predicted_rtp: f64,
    pub rtp_deviation: f64,
    pub iterations_used: u32,
    pub convergence_score: f64,
}


//======================================================================
//==                       HELPER FUNCTIONS                           ==
//======================================================================

static PRIZE_STRUCTURE: phf::Map<u8, phf::Map<u8, f64>> = phf::phf_map! {
    1u8 => phf::phf_map! { 1u8 => 4.0, 0u8 => 0.0 },
    2u8 => phf::phf_map! { 2u8 => 25.0, 1u8 => 1.0, 0u8 => 0.0 },
    3u8 => phf::phf_map! { 3u8 => 50.0, 2u8 => 5.0, 1u8 => 0.0, 0u8 => 0.0 },
    4u8 => phf::phf_map! { 4u8 => 110.0, 3u8 => 20.0, 2u8 => 1.0, 1u8 => 0.0, 0u8 => 0.0 },
    5u8 => phf::phf_map! { 5u8 => 900.0, 4u8 => 15.0, 3u8 => 3.0, 2u8 => 1.0, 1u8 => 0.0, 0u8 => 0.0 },
};

const TOTAL_NUMBERS: u8 = 80;
const DRAW_COUNT: usize = 20;
const HEADS_TAILS_MULTIPLIER: f64 = 2.0;
const EVENS_MULTIPLIER: f64 = 4.0;

// Enhanced RTP Control Constants
const RTP_CONVERGENCE_WEIGHT: f64 = 10.0;  // How strongly to pull toward target RTP
const RTP_DEVIATION_PENALTY: f64 = 5.0;    // Penalty for extreme deviations
const SAFETY_NET_THRESHOLD: f64 = 0.05;    // 5% below target triggers safety net
const CONVERGENCE_THRESHOLD: f64 = 0.001;  // Stop when improvement is minimal
const MIN_ITERATIONS: u32 = 100;           // Minimum iterations before early stop
const PAYOUT_SMOOTHING_FACTOR: f64 = 0.1;  // Smoothing for payout ratio calculation

#[derive(PartialEq)]
enum HeadsTailsEvensOutcome { Heads, Tails, Evens }

fn get_heads_tails_evens(drawn_numbers: &[u8]) -> HeadsTailsEvensOutcome {
    let count_under_41 = drawn_numbers.iter().filter(|&&n| n < 41).count();
    if count_under_41 > 10 { HeadsTailsEvensOutcome::Heads }
    else if count_under_41 < 10 { HeadsTailsEvensOutcome::Tails }
    else { HeadsTailsEvensOutcome::Evens }
}

fn calculate_winnings(selected_count: u8, matched_count: u8, bet_amount: f64) -> f64 {
    PRIZE_STRUCTURE
        .get(&selected_count)
        .and_then(|matches_map| matches_map.get(&matched_count))
        .map_or(0.0, |multiplier| multiplier * bet_amount)
}

fn generate_random_draw() -> Vec<u8> {
    let mut nums: Vec<u8> = (1..=TOTAL_NUMBERS).collect();
    let mut rng = rand::rng();
    nums.shuffle(&mut rng);
    nums.into_iter().take(DRAW_COUNT).collect()
}

fn simulate_payout_for_tickets(drawn_numbers: &[u8], tickets: &[Ticket]) -> f64 {
    let mut total_payout = 0.0;
    let drawn_set: HashSet<_> = drawn_numbers.iter().cloned().collect();
    let outcome = get_heads_tails_evens(drawn_numbers);

    for ticket in tickets {
        if let Some(bet_type) = &ticket.bet_type {
            let multiplier = match bet_type.as_str() {
                "HEADS" if outcome == HeadsTailsEvensOutcome::Heads => HEADS_TAILS_MULTIPLIER,
                "TAILS" if outcome == HeadsTailsEvensOutcome::Tails => HEADS_TAILS_MULTIPLIER,
                "EVENS" if outcome == HeadsTailsEvensOutcome::Evens => EVENS_MULTIPLIER,
                _ => 0.0,
            };
            total_payout += ticket.stake * multiplier;
        } else if let Some(selected_numbers) = &ticket.selected_numbers {
            let matches = selected_numbers.iter().filter(|n| drawn_set.contains(n)).count();
            total_payout += calculate_winnings(selected_numbers.len() as u8, matches as u8, ticket.stake);
        }
    }
    total_payout
}

// Enhanced RTP calculation with better target determination
fn calculate_target_rtp(shop: &ShopFinancials) -> f64 {
    let profit_target_rtp = (100.0 - shop.profit_margin) / 100.0;
    let minimum_floor_rtp = shop.min_safe_rtp / 100.0;
    profit_target_rtp.max(minimum_floor_rtp)
}

fn calculate_current_rtp(shop: &ShopFinancials) -> f64 {
    if shop.current_window_bets <= 0.0 {
        return 1.0; // Default to 100% when no data
    }
    shop.current_window_payouts / shop.current_window_bets
}

// Predict what RTP will be after this round
fn predict_rtp_after_round(shop: &ShopFinancials, round_payout: f64) -> f64 {
    let total_bets = shop.current_window_bets + shop.current_round_bets;
    let total_payouts = shop.current_window_payouts + round_payout;

    if total_bets <= 0.0 {
        return 1.0;
    }
    total_payouts / total_bets
}

// Enhanced fitness scoring with proportional RTP control
fn calculate_enhanced_fitness_score(
    shop: &ShopFinancials,
    config: &Config,
    payout: f64,
) -> f64 {
    let target_rtp = calculate_target_rtp(shop);
    let current_rtp = calculate_current_rtp(shop);
    let predicted_rtp = predict_rtp_after_round(shop, payout);

    let mut score = 0.0;

    // 1. RTP Convergence Score - reward moves toward target RTP
    let rtp_deviation_before = (target_rtp - current_rtp).abs();
    let rtp_deviation_after = (target_rtp - predicted_rtp).abs();
    let rtp_improvement = rtp_deviation_before - rtp_deviation_after;
    score += rtp_improvement * RTP_CONVERGENCE_WEIGHT;

    // 2. Proportional Distance Score - closer to target is better
    let distance_to_target = (target_rtp - predicted_rtp).abs();
    score += (1.0 - distance_to_target) * 2.0;

    // 3. Safety Net - strong bonus for shops dangerously below target
    if current_rtp < target_rtp - SAFETY_NET_THRESHOLD {
        let safety_bonus = if predicted_rtp > current_rtp {
            (predicted_rtp - current_rtp) * 20.0 // Strong reward for improvement
        } else {
            -10.0 // Penalty for making it worse
        };
        score += safety_bonus;
    }

    // 4. Extreme Deviation Penalty - prevent wild swings
    if predicted_rtp > target_rtp + 0.1 || predicted_rtp < target_rtp - 0.1 {
        score -= RTP_DEVIATION_PENALTY;
    }

    // 5. Smoothing bonus - prefer gradual changes over sudden jumps
    let payout_ratio = if shop.current_round_bets > 0.0 {
        payout / shop.current_round_bets
    } else {
        0.0
    };

    let smoothed_ratio = payout_ratio * PAYOUT_SMOOTHING_FACTOR +
                        (1.0 - PAYOUT_SMOOTHING_FACTOR) * current_rtp;
    let smoothing_bonus = 1.0 - (payout_ratio - smoothed_ratio).abs();
    score += smoothing_bonus;

    score
}

// Legacy function for backward compatibility
fn calculate_expected_probability(shop: &ShopFinancials, config: &Config) -> f64 {
    let target_rtp = calculate_target_rtp(shop);
    let current_rtp = calculate_current_rtp(shop);
    let rtp_deviation = target_rtp - current_rtp;
    let probability = 0.5 + rtp_deviation * config.sensitivity_factor;
    probability.clamp(config.min_payout_probability, config.max_payout_probability)
}


// Advanced optimization techniques
fn generate_guided_draw(target_payout_ratio: f64, tickets: &[Ticket]) -> Vec<u8> {
    let mut rng = rand::rng();
    let mut draw = generate_random_draw();

    // If we need higher payouts, try to include more numbers that players selected
    if target_payout_ratio > 0.5 {
        let mut player_numbers: Vec<u8> = Vec::new();
        for ticket in tickets {
            if let Some(selected) = &ticket.selected_numbers {
                player_numbers.extend(selected.iter().cloned());
            }
        }

        if !player_numbers.is_empty() {
            // Replace some random numbers with player-selected numbers
            let replacement_count = (draw.len() as f64 * target_payout_ratio * 0.3) as usize;
            for _ in 0..replacement_count.min(draw.len()).min(player_numbers.len()) {
                if let Some(&player_num) = player_numbers.choose(&mut rng) {
                    if !draw.contains(&player_num) {
                        if let Some(pos) = (0..draw.len()).choose(&mut rng) {
                            draw[pos] = player_num;
                        }
                    }
                }
            }
        }
    }

    draw
}

fn local_optimization(mut draw: Vec<u8>, tickets: &[Ticket], shop: &ShopFinancials, config: &Config) -> Vec<u8> {
    let mut best_draw = draw.clone();
    let mut best_score = {
        let payout = simulate_payout_for_tickets(&draw, tickets);
        calculate_enhanced_fitness_score(shop, config, payout)
    };

    // Try small modifications to improve the draw
    for _ in 0..20 { // Limited local search
        let mut candidate = draw.clone();
        let mut rng = rand::rng();

        // Randomly swap one number
        if let Some(&pos) = (0..candidate.len()).collect::<Vec<_>>().choose(&mut rng) {
            let new_num = rng.gen_range(1..=TOTAL_NUMBERS);
            if !candidate.contains(&new_num) {
                candidate[pos] = new_num;

                let payout = simulate_payout_for_tickets(&candidate, tickets);
                let score = calculate_enhanced_fitness_score(shop, config, payout);

                if score > best_score {
                    best_score = score;
                    best_draw = candidate;
                }
            }
        }
    }

    best_draw
}

//======================================================================
//==                       MAIN N-API FUNCTION                        ==
//======================================================================

#[napi]
pub fn run_simulation(input: SimulationInput) -> Result<SimulationOutput> {
    // Handle edge case of no bets in the current round
    if input.shop_financials.current_round_bets <= 0.0 {
        let random_draw = generate_random_draw();
        return Ok(SimulationOutput {
            final_draw: random_draw,
            final_payout: 0.0,
            predicted_rtp: calculate_current_rtp(&input.shop_financials),
            rtp_deviation: 0.0,
            iterations_used: 1,
            convergence_score: 1.0,
        });
    }

    let target_rtp = calculate_target_rtp(&input.shop_financials);
    let current_rtp = calculate_current_rtp(&input.shop_financials);
    let expected_probability = calculate_expected_probability(&input.shop_financials, &input.config);

    let mut best_draw: Option<Vec<u8>> = None;
    let mut best_score = f64::NEG_INFINITY;
    let mut best_payout = 0.0;
    let mut iterations_used = 0u32;
    let mut last_improvement_iteration = 0u32;

    // Multi-phase optimization approach
    for iteration in 0..input.config.max_draw_attempts {
        iterations_used = iteration + 1;

        // Phase 1: Random exploration (first 30% of iterations)
        // Phase 2: Guided search (middle 50% of iterations)
        // Phase 3: Local optimization (final 20% of iterations)
        let phase_ratio = iteration as f64 / input.config.max_draw_attempts as f64;

        let candidate_draw = if phase_ratio < 0.3 {
            // Random exploration
            generate_random_draw()
        } else if phase_ratio < 0.8 {
            // Guided search based on current RTP needs
            let target_payout_ratio = if current_rtp < target_rtp {
                0.7 // Need higher payouts
            } else {
                0.3 // Need lower payouts
            };
            generate_guided_draw(target_payout_ratio, &input.tickets)
        } else {
            // Local optimization of best candidate
            if let Some(ref best) = best_draw {
                local_optimization(best.clone(), &input.tickets, &input.shop_financials, &input.config)
            } else {
                generate_random_draw()
            }
        };

        let payout = simulate_payout_for_tickets(&candidate_draw, &input.tickets);
        let current_score = calculate_enhanced_fitness_score(&input.shop_financials, &input.config, payout);

        // Track improvements for convergence detection
        if current_score > best_score {
            best_score = current_score;
            best_draw = Some(candidate_draw);
            best_payout = payout;
            last_improvement_iteration = iteration;
        }

        // Early convergence detection
        if iteration >= MIN_ITERATIONS {
            let iterations_since_improvement = iteration - last_improvement_iteration;
            if iterations_since_improvement > input.config.max_draw_attempts / 10 {
                // No improvement for 10% of max iterations, likely converged
                break;
            }
        }

        // Adaptive convergence based on score improvement
        if iteration > 0 && iteration % 100 == 0 {
            let improvement_rate = best_score / (iteration as f64);
            if improvement_rate < CONVERGENCE_THRESHOLD {
                break;
            }
        }
    }

    // Fallback if no best draw found
    let final_draw = best_draw.unwrap_or_else(generate_random_draw);
    let final_payout = simulate_payout_for_tickets(&final_draw, &input.tickets);
    let predicted_rtp = predict_rtp_after_round(&input.shop_financials, final_payout);
    let rtp_deviation = (target_rtp - predicted_rtp).abs();
    let convergence_score = if iterations_used > 0 {
        best_score / iterations_used as f64
    } else {
        0.0
    };

    Ok(SimulationOutput {
        final_draw,
        final_payout,
        predicted_rtp,
        rtp_deviation,
        iterations_used,
        convergence_score,
    })
}

// Additional utility function for RTP analysis
#[napi]
pub fn analyze_rtp_impact(input: SimulationInput, test_draws: Vec<Vec<u8>>) -> Result<Vec<f64>> {
    let mut rtp_impacts = Vec::new();

    for draw in test_draws {
        if draw.len() != DRAW_COUNT {
            continue;
        }

        let payout = simulate_payout_for_tickets(&draw, &input.tickets);
        let predicted_rtp = predict_rtp_after_round(&input.shop_financials, payout);
        rtp_impacts.push(predicted_rtp);
    }

    Ok(rtp_impacts)
}

// Function to test convergence behavior
#[napi]
pub fn test_convergence_behavior(input: SimulationInput, max_iterations: u32) -> Result<Vec<f64>> {
    let mut scores = Vec::new();
    let mut best_score = f64::NEG_INFINITY;

    for iteration in 0..max_iterations {
        let candidate_draw = generate_random_draw();
        let payout = simulate_payout_for_tickets(&candidate_draw, &input.tickets);
        let score = calculate_enhanced_fitness_score(&input.shop_financials, &input.config, payout);

        if score > best_score {
            best_score = score;
        }

        scores.push(best_score);
    }

    Ok(scores)
}